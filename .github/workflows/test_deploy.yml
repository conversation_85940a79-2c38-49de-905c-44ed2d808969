name: Manual Build and Test

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'development'
        type: choice
        options:
          - development
          - staging
          - production

jobs:
  build:
    runs-on: ubuntu-latest

    # Docker Container für Cross-Platform Builds verwenden
    container:
      image: electronuserland/builder:wine

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Wine environment
        run: |
          # Create Wine prefix directory with proper permissions
          export WINEPREFIX=/tmp/.wine
          mkdir -p $WINEPREFIX
          chmod -R 755 $WINEPREFIX

          # Set ownership to current user
          chown -R $(whoami):$(whoami) $WINEPREFIX 2>/dev/null || true

          # Initialize Wine (this may take a moment)
          echo "Initializing Wine..."
          wine --version

          # Set Wine to not show GUI dialogs and run in headless mode
          export WINEDLLOVERRIDES="mscoree,mshtml="
          export DISPLAY=:99

          # Create a dummy registry to avoid permission issues
          echo "Setting up Wine registry..."
          wineboot --init || true

          # Verify Wine is working
          echo "Wine setup complete"
        env:
          WINEPREFIX: /tmp/.wine
          WINEDLLOVERRIDES: "mscoree,mshtml="
          DISPLAY: ":99"

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        # Nutzen der Version aus package.json

      - name: Setup Node.js with pnpm cache
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install

      - name: Build application
        run: |
          echo "Starting build process..."
          echo "Wine prefix: $WINEPREFIX"
          echo "Current user: $(whoami)"
          echo "Wine version: $(wine --version)"

          # Run the build
          pnpm build:win
        env:
          # Electron Cache für bessere Performance
          ELECTRON_CACHE: "/tmp/.cache/electron"
          ELECTRON_BUILDER_CACHE: "/tmp/.cache/electron-builder"
          # Wine configuration
          WINEPREFIX: /tmp/.wine
          WINEDLLOVERRIDES: "mscoree,mshtml="
          DISPLAY: ":99"
          # Disable code signing for now to avoid Wine issues
          CSC_IDENTITY_AUTO_DISCOVERY: false

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: build-files-${{ inputs.environment }}
          path: |
            dist/
            out/
          retention-days: 30
